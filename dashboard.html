<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - Work Management System</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <link rel="stylesheet" href="assets/css/analytics.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    
    <style>
        /* Custom styles for dashboard */
        body {
            background: linear-gradient(135deg, var(--secondary-50) 0%, var(--secondary-100) 100%);
            min-height: 100vh;
            padding: var(--space-4);
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .dashboard-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
        }

        .dashboard-title i {
            color: var(--primary-600);
        }

        .dashboard-subtitle {
            font-size: var(--font-size-lg);
            color: var(--secondary-600);
            font-weight: var(--font-weight-medium);
        }

        .back-link {
            position: absolute;
            top: var(--space-4);
            left: var(--space-4);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--secondary-600);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: color var(--transition-fast);
        }

        .back-link:hover {
            color: var(--primary-600);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .metric-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-600);
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }

        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            color: white;
        }

        .metric-icon.tasks {
            background: var(--success-600);
        }

        .metric-icon.equipment {
            background: var(--primary-600);
        }

        .metric-icon.productivity {
            background: var(--warning-600);
        }

        .metric-icon.performance {
            background: var(--error-600);
        }

        .metric-value {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-1);
        }

        .metric-label {
            font-size: var(--font-size-sm);
            color: var(--secondary-600);
            font-weight: var(--font-weight-medium);
        }

        .metric-change {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .metric-change.positive {
            color: var(--success-600);
        }

        .metric-change.negative {
            color: var(--error-600);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        @media (max-width: 1024px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        .chart-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
        }

        .chart-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: var(--space-2);
        }

        .chart-control {
            padding: var(--space-2) var(--space-3);
            border: 1px solid var(--secondary-300);
            background: white;
            border-radius: var(--radius-md);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .chart-control.active {
            background: var(--primary-600);
            color: white;
            border-color: var(--primary-600);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .chart-container.large {
            height: 400px;
        }

        .reports-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
        }

        .reports-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: var(--space-6);
        }

        .reports-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .report-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .report-actions {
            display: flex;
            gap: var(--space-3);
            flex-wrap: wrap;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-xl);
            z-index: 10;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .refresh-button {
            position: fixed;
            bottom: var(--space-6);
            right: var(--space-6);
            width: 56px;
            height: 56px;
            border-radius: var(--radius-full);
            background: var(--primary-600);
            color: white;
            border: none;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
        }

        .refresh-button:hover {
            background: var(--primary-700);
            transform: scale(1.05);
        }

        .refresh-button.spinning {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--space-2);
            }
            
            .dashboard-title {
                font-size: var(--font-size-3xl);
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }
            
            .chart-container {
                height: 250px;
            }
            
            .chart-container.large {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Dashboard
    </a>
    
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-chart-line"></i>
                Analytics Dashboard
            </h1>
            <p class="dashboard-subtitle">Comprehensive insights into your work management system</p>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon tasks">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="metric-change positive" id="tasksChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12%</span>
                    </div>
                </div>
                <div class="metric-value" id="totalTasks">0</div>
                <div class="metric-label">Total Tasks Completed</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon equipment">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="metric-change positive" id="equipmentChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+5%</span>
                    </div>
                </div>
                <div class="metric-value" id="totalEquipment">0</div>
                <div class="metric-label">Equipment Items Managed</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon productivity">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="metric-change positive" id="productivityChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8%</span>
                    </div>
                </div>
                <div class="metric-value" id="productivityScore">85%</div>
                <div class="metric-label">Productivity Score</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon performance">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="metric-change negative" id="responseChange">
                        <i class="fas fa-arrow-down"></i>
                        <span>-3%</span>
                    </div>
                </div>
                <div class="metric-value" id="avgResponseTime">2.4h</div>
                <div class="metric-label">Avg Response Time</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Task Completion Trends</h3>
                    <div class="chart-controls">
                        <button class="chart-control active" data-period="7d">7 Days</button>
                        <button class="chart-control" data-period="30d">30 Days</button>
                        <button class="chart-control" data-period="90d">90 Days</button>
                    </div>
                </div>
                <div class="chart-container large">
                    <div class="loading-overlay" id="tasksChartLoading">
                        <div class="spinner"></div>
                    </div>
                    <canvas id="tasksChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Task Categories</h3>
                </div>
                <div class="chart-container">
                    <div class="loading-overlay" id="categoriesChartLoading">
                        <div class="spinner"></div>
                    </div>
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Equipment Analytics -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Equipment Status Overview</h3>
                </div>
                <div class="chart-container">
                    <div class="loading-overlay" id="equipmentChartLoading">
                        <div class="spinner"></div>
                    </div>
                    <canvas id="equipmentChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Upgrade Requests</h3>
                    <div class="chart-controls">
                        <button class="chart-control active" data-status="all">All</button>
                        <button class="chart-control" data-status="pending">Pending</button>
                        <button class="chart-control" data-status="approved">Approved</button>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="loading-overlay" id="upgradesChartLoading">
                        <div class="spinner"></div>
                    </div>
                    <canvas id="upgradesChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Task Archival Management Section -->
        <div class="reports-section" style="margin-bottom: var(--space-6);">
            <div class="reports-header">
                <h3 class="reports-title">
                    <i class="fas fa-archive"></i>
                    Task Archival Management
                </h3>
            </div>

            <div class="archival-info" id="archivalInfo">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>Automatic Daily Archival:</strong> Your system automatically archives completed tasks at the end of each day, keeping your active task list clean while preserving all data for analytics.
                        <div style="margin-top: var(--space-2); font-size: var(--font-size-sm);">
                            <span id="archivalStatusDashboard">Loading archival status...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="performManualArchival()">
                    <i class="fas fa-archive"></i>
                    Archive Completed Tasks
                </button>
                <button class="btn btn-secondary" onclick="viewArchivalHistory()">
                    <i class="fas fa-history"></i>
                    View Archival History
                </button>
                <button class="btn btn-warning" onclick="migrateExistingTasks()">
                    <i class="fas fa-database"></i>
                    Migrate Existing Tasks
                </button>
            </div>
        </div>

        <!-- Daily Snapshots Section -->
        <div class="reports-section" style="margin-bottom: var(--space-6);">
            <div class="reports-header">
                <h3 class="reports-title">
                    <i class="fas fa-camera"></i>
                    Daily Snapshots
                </h3>
            </div>

            <div class="snapshot-info" id="snapshotInfo">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Automatic Daily Snapshots:</strong> Your system automatically captures daily snapshots of tasks, equipment, and performance metrics for historical analysis.
                        <div style="margin-top: var(--space-2); font-size: var(--font-size-sm);">
                            <span id="snapshotStatus">Loading snapshot status...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="createManualSnapshot()">
                    <i class="fas fa-camera"></i>
                    Create Manual Snapshot
                </button>
                <button class="btn btn-secondary" onclick="viewSnapshotHistory()">
                    <i class="fas fa-history"></i>
                    View History
                </button>
                <button class="btn btn-secondary" onclick="backfillSnapshots()">
                    <i class="fas fa-database"></i>
                    Backfill Historical Data
                </button>
            </div>
        </div>

        <!-- Manual Reports Section -->
        <div class="reports-section" style="margin-bottom: var(--space-6);">
            <div class="reports-header">
                <h3 class="reports-title">
                    <i class="fas fa-file-alt"></i>
                    Manual Reports
                </h3>
            </div>

            <div class="manual-reports-info">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Custom Report Generation:</strong> Create detailed reports with problem descriptions, solutions, and professional signatures for documentation and approval workflows.
                        <div style="margin-top: var(--space-2); font-size: var(--font-size-sm);">
                            <span id="manualReportsStatus">Loading reports status...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-actions">
                <a href="manual-report.html" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create New Report
                </a>
                <button class="btn btn-secondary" onclick="viewManualReports()">
                    <i class="fas fa-list"></i>
                    View All Reports
                </button>
                <button class="btn btn-secondary" onclick="searchManualReports()">
                    <i class="fas fa-search"></i>
                    Search Reports
                </button>
            </div>
        </div>

        <!-- Automated Reports Section -->
        <div class="reports-section">
            <div class="reports-header">
                <h3 class="reports-title">
                    <i class="fas fa-chart-bar"></i>
                    Automated Reports
                </h3>
            </div>

            <div class="report-filters">
                <div class="form-group">
                    <label for="reportType" class="form-label">Report Type</label>
                    <select id="reportType" class="form-select">
                        <option value="tasks">Task Summary</option>
                        <option value="equipment">Equipment Report</option>
                        <option value="productivity">Productivity Analysis</option>
                        <option value="comprehensive">Comprehensive Report</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" id="dateFrom" class="form-input">
                </div>

                <div class="form-group">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" id="dateTo" class="form-input">
                </div>

                <div class="form-group">
                    <label for="reportFormat" class="form-label">Format</label>
                    <select id="reportFormat" class="form-select">
                        <option value="pdf">PDF Report</option>
                        <option value="csv">CSV Export</option>
                        <option value="excel">Excel Spreadsheet</option>
                    </select>
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-download"></i>
                    Generate Report
                </button>
                <button class="btn btn-secondary" onclick="previewReport()">
                    <i class="fas fa-eye"></i>
                    Preview Report
                </button>
                <button class="btn btn-secondary" onclick="scheduleReport()">
                    <i class="fas fa-calendar"></i>
                    Schedule Report
                </button>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-button" onclick="refreshDashboard()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Design System JavaScript -->
    <script src="assets/js/design-system.js"></script>
    <script src="assets/js/daily-scheduler.js"></script>

    <script>
        // Dashboard Analytics System
        class DashboardAnalytics {
            constructor() {
                this.charts = {};
                this.data = {
                    tasks: [],
                    equipment: [],
                    upgrades: []
                };
                this.API_BASE = '/.netlify/functions';
                this.init();
            }

            async init() {
                await this.loadData();
                this.initializeCharts();
                this.updateMetrics();
                this.setupEventListeners();
                this.startAutoRefresh();
            }

            async loadData() {
                try {
                    // Load current data
                    const [tasksResponse, equipmentResponse, upgradesResponse] = await Promise.all([
                        fetch(`${this.API_BASE}/get-todos`),
                        fetch(`${this.API_BASE}/get-equipment`),
                        fetch(`${this.API_BASE}/get-upgrades`)
                    ]);

                    if (tasksResponse.ok) {
                        this.data.tasks = await tasksResponse.json();
                    }
                    if (equipmentResponse.ok) {
                        this.data.equipment = await equipmentResponse.json();
                    }
                    if (upgradesResponse.ok) {
                        this.data.upgrades = await upgradesResponse.json();
                    }

                    // Load historical data for trends
                    await this.loadHistoricalData();

                } catch (error) {
                    console.error('Error loading data:', error);
                    this.showAlert('Error loading dashboard data', 'error');
                }
            }

            async loadHistoricalData() {
                try {
                    // Get daily scheduler instance
                    const scheduler = window.dailySchedulerInstance || DailyScheduler.getInstance();

                    // Load historical snapshots for the last 30 days
                    this.historicalData = await scheduler.getHistoricalData('30d');

                    // Load specific metrics for charts
                    this.chartData = await scheduler.getSpecificMetrics(
                        ['tasks', 'equipment', 'productivity', 'upgrades'],
                        null, null
                    );

                } catch (error) {
                    console.error('Error loading historical data:', error);
                    // Fallback to current data only
                    this.historicalData = null;
                    this.chartData = null;
                }
            }

            updateMetrics() {
                // Update task metrics
                const completedTasks = this.data.tasks.filter(task => task.completed).length;
                document.getElementById('totalTasks').textContent = completedTasks;

                // Update equipment metrics
                document.getElementById('totalEquipment').textContent = this.data.equipment.length;

                // Calculate productivity score
                const totalTasks = this.data.tasks.length;
                const productivityScore = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
                document.getElementById('productivityScore').textContent = `${productivityScore}%`;

                // Calculate average response time (mock calculation)
                const avgResponseTime = this.calculateAverageResponseTime();
                document.getElementById('avgResponseTime').textContent = avgResponseTime;
            }

            calculateAverageResponseTime() {
                // Mock calculation - in real implementation, this would use actual timestamps
                const responseTimes = [1.2, 2.4, 3.1, 1.8, 2.9, 2.2, 1.9];
                const avg = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                return `${avg.toFixed(1)}h`;
            }

            initializeCharts() {
                this.createTasksChart();
                this.createCategoriesChart();
                this.createEquipmentChart();
                this.createUpgradesChart();
            }

            createTasksChart() {
                const ctx = document.getElementById('tasksChart').getContext('2d');
                const chartData = this.generateTasksChartData();

                this.charts.tasks = new Chart(ctx, {
                    type: 'line',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            }
                        },
                        elements: {
                            line: {
                                tension: 0.4
                            }
                        }
                    }
                });

                document.getElementById('tasksChartLoading').classList.add('hidden');
            }

            createCategoriesChart() {
                const ctx = document.getElementById('categoriesChart').getContext('2d');
                const chartData = this.generateCategoriesChartData();

                this.charts.categories = new Chart(ctx, {
                    type: 'doughnut',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });

                document.getElementById('categoriesChartLoading').classList.add('hidden');
            }

            createEquipmentChart() {
                const ctx = document.getElementById('equipmentChart').getContext('2d');
                const chartData = this.generateEquipmentChartData();

                this.charts.equipment = new Chart(ctx, {
                    type: 'bar',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                document.getElementById('equipmentChartLoading').classList.add('hidden');
            }

            createUpgradesChart() {
                const ctx = document.getElementById('upgradesChart').getContext('2d');
                const chartData = this.generateUpgradesChartData();

                this.charts.upgrades = new Chart(ctx, {
                    type: 'pie',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });

                document.getElementById('upgradesChartLoading').classList.add('hidden');
            }

            generateTasksChartData(period = '7d') {
                // Use historical data if available
                if (this.chartData && this.chartData.tasks) {
                    const historicalTasks = this.chartData.tasks;

                    // Filter by period
                    const filteredData = this.filterDataByPeriod(historicalTasks, period);

                    return {
                        labels: filteredData.map(item => {
                            const date = new Date(item.date);
                            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                        }),
                        datasets: [
                            {
                                label: 'Completed Tasks',
                                data: filteredData.map(item => item.completed),
                                borderColor: '#22c55e',
                                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: 'Total Tasks',
                                data: filteredData.map(item => item.total),
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true,
                                tension: 0.4
                            }
                        ]
                    };
                }

                // Fallback to mock data
                return this.generateMockTasksData(period);
            }

            filterDataByPeriod(data, period) {
                const now = new Date();
                let days;

                switch (period) {
                    case '7d': days = 7; break;
                    case '30d': days = 30; break;
                    case '90d': days = 90; break;
                    default: days = 7;
                }

                const cutoffDate = new Date(now);
                cutoffDate.setDate(cutoffDate.getDate() - days);

                return data
                    .filter(item => new Date(item.date) >= cutoffDate)
                    .sort((a, b) => new Date(a.date) - new Date(b.date))
                    .slice(-days); // Take last N days
            }

            generateMockTasksData(period) {
                const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;
                const labels = [];
                const completedTasks = [];
                const totalTasks = [];

                for (let i = days - 1; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));

                    completedTasks.push(Math.floor(Math.random() * 10) + 5);
                    totalTasks.push(Math.floor(Math.random() * 5) + 15);
                }

                return {
                    labels,
                    datasets: [
                        {
                            label: 'Completed Tasks',
                            data: completedTasks,
                            borderColor: '#22c55e',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Total Tasks',
                            data: totalTasks,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                };
            }

            generateCategoriesChartData() {
                // Analyze task categories based on content
                const categories = {
                    'Technical Issues': 0,
                    'Maintenance': 0,
                    'User Support': 0,
                    'System Updates': 0,
                    'Other': 0
                };

                this.data.tasks.forEach(task => {
                    const content = (task.task + ' ' + task.problem + ' ' + task.impact).toLowerCase();
                    if (content.includes('technical') || content.includes('bug') || content.includes('error')) {
                        categories['Technical Issues']++;
                    } else if (content.includes('maintenance') || content.includes('update') || content.includes('patch')) {
                        categories['Maintenance']++;
                    } else if (content.includes('user') || content.includes('support') || content.includes('help')) {
                        categories['User Support']++;
                    } else if (content.includes('system') || content.includes('server') || content.includes('network')) {
                        categories['System Updates']++;
                    } else {
                        categories['Other']++;
                    }
                });

                return {
                    labels: Object.keys(categories),
                    datasets: [{
                        data: Object.values(categories),
                        backgroundColor: [
                            '#ef4444',
                            '#f59e0b',
                            '#22c55e',
                            '#3b82f6',
                            '#8b5cf6'
                        ]
                    }]
                };
            }

            generateEquipmentChartData() {
                // Group equipment by status
                const statusCounts = {
                    'Active': 0,
                    'Maintenance': 0,
                    'Retired': 0,
                    'Pending': 0
                };

                this.data.equipment.forEach(item => {
                    const status = item.status || 'Active';
                    if (statusCounts.hasOwnProperty(status)) {
                        statusCounts[status]++;
                    } else {
                        statusCounts['Active']++;
                    }
                });

                return {
                    labels: Object.keys(statusCounts),
                    datasets: [{
                        label: 'Equipment Count',
                        data: Object.values(statusCounts),
                        backgroundColor: [
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#6b7280'
                        ]
                    }]
                };
            }

            generateUpgradesChartData() {
                // Group upgrades by status
                const statusCounts = {
                    'Pending': 0,
                    'Approved': 0,
                    'Rejected': 0,
                    'Completed': 0
                };

                this.data.upgrades.forEach(upgrade => {
                    const status = upgrade.status || 'Pending';
                    if (statusCounts.hasOwnProperty(status)) {
                        statusCounts[status]++;
                    } else {
                        statusCounts['Pending']++;
                    }
                });

                return {
                    labels: Object.keys(statusCounts),
                    datasets: [{
                        data: Object.values(statusCounts),
                        backgroundColor: [
                            '#f59e0b',
                            '#22c55e',
                            '#ef4444',
                            '#3b82f6'
                        ]
                    }]
                };
            }

            setupEventListeners() {
                // Chart period controls
                document.querySelectorAll('.chart-control').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const parent = e.target.closest('.chart-controls');
                        parent.querySelectorAll('.chart-control').forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');

                        // Update chart based on period
                        const period = e.target.dataset.period;
                        if (period) {
                            this.updateChartPeriod(period);
                        }
                    });
                });

                // Set default date range for reports
                const today = new Date();
                const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

                document.getElementById('dateFrom').value = lastMonth.toISOString().split('T')[0];
                document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            }

            updateChartPeriod(period) {
                // Update tasks chart with new period
                if (this.charts.tasks) {
                    const newData = this.generateTasksChartData(period);
                    this.charts.tasks.data = newData;
                    this.charts.tasks.update('active');
                }

                // Update other charts if they support period filtering
                if (this.charts.productivity && this.chartData?.productivity) {
                    const productivityData = this.filterDataByPeriod(this.chartData.productivity, period);
                    this.charts.productivity.data.labels = productivityData.map(item => {
                        const date = new Date(item.date);
                        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    });
                    this.charts.productivity.data.datasets[0].data = productivityData.map(item => item.efficiency);
                    this.charts.productivity.update('active');
                }
            }

            startAutoRefresh() {
                // Refresh data every 5 minutes
                setInterval(() => {
                    this.refreshDashboard();
                }, 5 * 60 * 1000);
            }

            async refreshDashboard() {
                const refreshButton = document.querySelector('.refresh-button');
                refreshButton.classList.add('spinning');

                try {
                    await this.loadData();
                    this.updateMetrics();

                    // Update all charts
                    Object.values(this.charts).forEach(chart => {
                        if (chart && chart.update) {
                            chart.update();
                        }
                    });

                    this.showAlert('Dashboard refreshed successfully', 'success');
                } catch (error) {
                    console.error('Error refreshing dashboard:', error);
                    this.showAlert('Error refreshing dashboard', 'error');
                } finally {
                    refreshButton.classList.remove('spinning');
                }
            }

            showAlert(message, type = 'info') {
                // Use design system alert functionality
                if (window.designSystemInstance) {
                    window.designSystemInstance.showAlert(message, type, {
                        autoDismiss: true,
                        dismissible: true
                    });
                } else {
                    alert(message);
                }
            }
        }

        // Global functions for report generation
        async function generateReport() {
            const reportType = document.getElementById('reportType').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const format = document.getElementById('reportFormat').value;

            if (!dateFrom || !dateTo) {
                alert('Please select both start and end dates');
                return;
            }

            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner"></span> Generating...';
            button.disabled = true;

            try {
                // Simulate report generation
                await new Promise(resolve => setTimeout(resolve, 2000));

                // In a real implementation, this would call an API endpoint
                const reportData = await generateReportData(reportType, dateFrom, dateTo);

                if (format === 'pdf') {
                    downloadPDFReport(reportData, reportType);
                } else if (format === 'csv') {
                    downloadCSVReport(reportData, reportType);
                } else if (format === 'excel') {
                    downloadExcelReport(reportData, reportType);
                }

                dashboardInstance.showAlert('Report generated successfully', 'success');
            } catch (error) {
                console.error('Error generating report:', error);
                dashboardInstance.showAlert('Error generating report', 'error');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        async function generateReportData(type, dateFrom, dateTo) {
            // Mock report data generation
            const data = {
                type: type,
                dateRange: { from: dateFrom, to: dateTo },
                generatedAt: new Date().toISOString(),
                summary: {
                    totalTasks: dashboardInstance.data.tasks.length,
                    completedTasks: dashboardInstance.data.tasks.filter(t => t.completed).length,
                    totalEquipment: dashboardInstance.data.equipment.length,
                    totalUpgrades: dashboardInstance.data.upgrades.length
                },
                details: {
                    tasks: dashboardInstance.data.tasks,
                    equipment: dashboardInstance.data.equipment,
                    upgrades: dashboardInstance.data.upgrades
                }
            };

            return data;
        }

        function downloadPDFReport(data, type) {
            // Mock PDF generation - in real implementation, use jsPDF or server-side generation
            const content = `
                ${type.toUpperCase()} REPORT
                Generated: ${new Date().toLocaleDateString()}
                Date Range: ${data.dateRange.from} to ${data.dateRange.to}

                SUMMARY:
                - Total Tasks: ${data.summary.totalTasks}
                - Completed Tasks: ${data.summary.completedTasks}
                - Total Equipment: ${data.summary.totalEquipment}
                - Total Upgrades: ${data.summary.totalUpgrades}
            `;

            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${type}-report-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function downloadCSVReport(data, type) {
            let csvContent = '';

            if (type === 'tasks' || type === 'comprehensive') {
                csvContent += 'Task,Problem,Solution,Status,Date\n';
                data.details.tasks.forEach(task => {
                    csvContent += `"${task.task || ''}","${task.problem || ''}","${task.impact || ''}","${task.completed ? 'Completed' : 'Pending'}","${task.createdAt || new Date().toISOString()}"\n`;
                });
            }

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${type}-report-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function downloadExcelReport(data, type) {
            // Mock Excel generation - in real implementation, use SheetJS or similar
            downloadCSVReport(data, type); // Fallback to CSV for now
        }

        function previewReport() {
            const reportType = document.getElementById('reportType').value;
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;

            if (!dateFrom || !dateTo) {
                alert('Please select both start and end dates');
                return;
            }

            // Open preview in new window
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>Report Preview - ${reportType}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        h1 { color: #333; }
                        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h1>${reportType.toUpperCase()} REPORT PREVIEW</h1>
                    <p><strong>Date Range:</strong> ${dateFrom} to ${dateTo}</p>
                    <div class="summary">
                        <h3>Summary</h3>
                        <p>Total Tasks: ${dashboardInstance.data.tasks.length}</p>
                        <p>Completed Tasks: ${dashboardInstance.data.tasks.filter(t => t.completed).length}</p>
                        <p>Total Equipment: ${dashboardInstance.data.equipment.length}</p>
                    </div>
                    <p><em>This is a preview. Use "Generate Report" to download the full report.</em></p>
                </body>
                </html>
            `);
        }

        function scheduleReport() {
            dashboardInstance.showAlert('Report scheduling feature coming soon!', 'info');
        }

        function refreshDashboard() {
            if (dashboardInstance) {
                dashboardInstance.refreshDashboard();
            }
        }

        // Archival management functions
        async function performManualArchival() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner"></span> Archiving...';
            button.disabled = true;

            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const result = await lifecycleManager.performManualArchival('completed');

                if (result.archived > 0) {
                    dashboardInstance.showAlert(`Successfully archived ${result.archived} completed tasks`, 'success');
                    // Refresh dashboard data
                    await dashboardInstance.loadData();
                    dashboardInstance.updateMetrics();
                } else {
                    dashboardInstance.showAlert('No completed tasks to archive', 'info');
                }

                updateArchivalStatusDashboard();
            } catch (error) {
                console.error('Error archiving tasks:', error);
                dashboardInstance.showAlert('Error archiving tasks', 'error');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        async function viewArchivalHistory() {
            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const archivedData = await lifecycleManager.getArchivedTasks({
                    period: '90d',
                    includeStats: true,
                    limit: 100
                });

                // Open archival history in new window
                const historyWindow = window.open('', '_blank', 'width=1200,height=800');
                historyWindow.document.write(generateArchivalHistoryHTML(archivedData));
            } catch (error) {
                console.error('Error viewing archival history:', error);
                dashboardInstance.showAlert('Error loading archival history', 'error');
            }
        }

        async function migrateExistingTasks() {
            try {
                // First, preview the migration
                const previewResponse = await fetch('/.netlify/functions/migrate-existing-tasks?mode=preview&daysBack=30&strategy=smart');
                if (!previewResponse.ok) {
                    throw new Error('Failed to preview migration');
                }

                const preview = await previewResponse.json();

                if (preview.totalTasks === 0) {
                    dashboardInstance.showAlert('No existing tasks found to migrate', 'info');
                    return;
                }

                const confirmMessage = `Migration Preview:

Total Tasks: ${preview.totalTasks}
Tasks to Archive: ${preview.summary.tasksToArchive}
Tasks to Keep Active: ${preview.summary.tasksToKeep}
Days to Process: ${preview.summary.daysToProcess}

This will organize your existing tasks by moving older completed tasks to the archive. Continue?`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="spinner"></span> Migrating...';
                button.disabled = true;

                // Execute migration
                const executeResponse = await fetch('/.netlify/functions/migrate-existing-tasks?mode=execute&daysBack=30&strategy=smart');
                if (!executeResponse.ok) {
                    throw new Error('Migration failed');
                }

                const result = await executeResponse.json();

                dashboardInstance.showAlert(`Migration completed! Migrated ${result.totalMigrated} tasks to archive.`, 'success');

                // Refresh dashboard data
                await dashboardInstance.loadData();
                dashboardInstance.updateMetrics();

                button.innerHTML = originalText;
                button.disabled = false;

            } catch (error) {
                console.error('Error during migration:', error);
                dashboardInstance.showAlert('Error during task migration', 'error');

                const button = event.target;
                button.innerHTML = button.getAttribute('data-original-text') || 'Migrate Existing Tasks';
                button.disabled = false;
            }
        }

        function generateArchivalHistoryHTML(data) {
            const tasks = data.tasks || [];
            const stats = data.statistics || {};

            return `
                <html>
                <head>
                    <title>Task Archival History</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
                        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
                        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
                        .stat-label { color: #666; font-size: 0.9em; }
                        .task-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: white; }
                        .task-header { font-weight: bold; color: #333; margin-bottom: 8px; }
                        .task-meta { font-size: 0.9em; color: #666; margin-bottom: 10px; }
                        .task-content { margin-bottom: 10px; }
                        .completed { background: #d4edda; border-color: #c3e6cb; }
                        .incomplete { background: #fff3cd; border-color: #ffeaa7; }
                        .migrated { background: #e2e3e5; border-color: #d6d8db; }
                        .filters { margin-bottom: 20px; }
                        .filter-btn { padding: 8px 16px; margin: 5px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 4px; }
                        .filter-btn.active { background: #007bff; color: white; }
                    </style>
                    <script>
                        function filterTasks(status) {
                            const tasks = document.querySelectorAll('.task-item');
                            const buttons = document.querySelectorAll('.filter-btn');

                            buttons.forEach(btn => btn.classList.remove('active'));
                            event.target.classList.add('active');

                            tasks.forEach(task => {
                                if (status === 'all') {
                                    task.style.display = 'block';
                                } else {
                                    task.style.display = task.classList.contains(status) ? 'block' : 'none';
                                }
                            });
                        }
                    </script>
                </head>
                <body>
                    <div class="header">
                        <h1>Task Archival History (Last 90 Days)</h1>
                        <p>Complete history of archived tasks from your daily work management system.</p>
                    </div>

                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-value">${stats.totalTasks || 0}</div>
                            <div class="stat-label">Total Archived</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.completedTasks || 0}</div>
                            <div class="stat-label">Completed Tasks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.completionRate || 0}%</div>
                            <div class="stat-label">Completion Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.manualArchives || 0}</div>
                            <div class="stat-label">Manual Archives</div>
                        </div>
                    </div>

                    <div class="filters">
                        <button class="filter-btn active" onclick="filterTasks('all')">All Tasks</button>
                        <button class="filter-btn" onclick="filterTasks('completed')">Completed</button>
                        <button class="filter-btn" onclick="filterTasks('incomplete')">Incomplete</button>
                        <button class="filter-btn" onclick="filterTasks('migrated')">Migrated</button>
                    </div>

                    <h3>Archived Tasks</h3>
                    ${tasks.map(task => `
                        <div class="task-item ${task.completed ? 'completed' : 'incomplete'} ${task.isMigrated ? 'migrated' : ''}">
                            <div class="task-header">${task.task || 'Untitled Task'}</div>
                            <div class="task-meta">
                                Archived: ${new Date(task.archivedAt).toLocaleDateString()} |
                                Status: ${task.completed ? 'Completed' : 'Incomplete'} |
                                Category: ${task.category || 'Other'} |
                                ${task.isMigrated ? 'Migrated | ' : ''}
                                Reason: ${task.archiveReason || 'Daily archival'}
                            </div>
                            ${task.problem ? `<div class="task-content"><strong>Problem:</strong> ${task.problem}</div>` : ''}
                            ${task.impact ? `<div class="task-content"><strong>Solution:</strong> ${task.impact}</div>` : ''}
                        </div>
                    `).join('')}

                    ${tasks.length === 0 ? '<p style="text-align: center; color: #666; font-style: italic;">No archived tasks found.</p>' : ''}
                </body>
                </html>
            `;
        }

        function updateArchivalStatusDashboard() {
            const statusElement = document.getElementById('archivalStatusDashboard');
            if (!statusElement) return;

            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const status = lifecycleManager.getLifecycleStatus();

                const nextArchival = new Date(status.nextArchival);
                const lastArchival = status.lastArchival || 'Never';

                statusElement.innerHTML = `
                    Last archival: <strong>${lastArchival}</strong> |
                    Next archival: <strong>${nextArchival.toLocaleDateString()} at ${nextArchival.toLocaleTimeString()}</strong> |
                    Strategy: <strong>${status.archivalStrategy}</strong> |
                    Auto-archival: <strong>${status.autoArchival ? 'Enabled' : 'Disabled'}</strong>
                `;
            } catch (error) {
                statusElement.innerHTML = 'Error loading archival status';
            }
        }

        // Manual Reports Management Functions
        async function viewManualReports() {
            try {
                const response = await fetch('/.netlify/functions/get-manual-reports?includeStats=true&limit=20');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // Open reports in new window
                const reportsWindow = window.open('', '_blank', 'width=1200,height=800');
                reportsWindow.document.write(generateManualReportsHTML(data));
            } catch (error) {
                console.error('Error viewing manual reports:', error);
                dashboardInstance.showAlert('Error loading manual reports', 'error');
            }
        }

        async function searchManualReports() {
            const searchTerm = prompt('Enter search term (searches titles, problems, and solutions):');
            if (!searchTerm || searchTerm.trim() === '') return;

            try {
                // For now, we'll use the basic get function and filter client-side
                // In a real implementation, you'd create a dedicated search endpoint
                const response = await fetch('/.netlify/functions/get-manual-reports?limit=100');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // Filter reports client-side
                const filteredReports = data.reports.filter(report => {
                    const titleMatch = report.title.toLowerCase().includes(searchTerm.toLowerCase());
                    const legacyMatch = (report.problem && report.problem.toLowerCase().includes(searchTerm.toLowerCase())) ||
                                       (report.solution && report.solution.toLowerCase().includes(searchTerm.toLowerCase())) ||
                                       (report.additional && report.additional.toLowerCase().includes(searchTerm.toLowerCase()));

                    // Check problems array
                    const problemsMatch = report.problems && report.problems.some(problem =>
                        (problem.name && problem.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
                        (problem.description && problem.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
                        (problem.solution && problem.solution.toLowerCase().includes(searchTerm.toLowerCase())) ||
                        (problem.additional && problem.additional.toLowerCase().includes(searchTerm.toLowerCase()))
                    );

                    return titleMatch || legacyMatch || problemsMatch;
                });

                if (filteredReports.length === 0) {
                    dashboardInstance.showAlert(`No reports found matching "${searchTerm}"`, 'info');
                    return;
                }

                // Open filtered results in new window
                const searchResults = { ...data, reports: filteredReports };
                const resultsWindow = window.open('', '_blank', 'width=1200,height=800');
                resultsWindow.document.write(generateManualReportsHTML(searchResults, `Search Results for "${searchTerm}"`));
            } catch (error) {
                console.error('Error searching manual reports:', error);
                dashboardInstance.showAlert('Error searching manual reports', 'error');
            }
        }

        function generateManualReportsHTML(data, title = 'Manual Reports') {
            const reports = data.reports || [];
            const stats = data.statistics || {};

            return `
                <html>
                <head>
                    <title>${title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
                        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
                        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
                        .stat-label { color: #666; font-size: 0.9em; }
                        .report-item { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; background: white; }
                        .report-header { display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }
                        .report-title { font-size: 1.3em; font-weight: bold; color: #333; margin: 0; }
                        .report-meta { font-size: 0.9em; color: #666; }
                        .report-section { margin: 15px 0; }
                        .report-section-title { font-weight: bold; color: #555; margin-bottom: 8px; }
                        .report-content { color: #333; white-space: pre-wrap; }
                        .signatures { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; }
                        .signature { text-align: center; }
                        .signature-name { font-family: 'Brush Script MT', cursive; font-size: 1.2em; color: #333; }
                        .signature-label { font-size: 0.9em; color: #666; margin-bottom: 5px; }
                        .completeness-bar { width: 100%; height: 8px; background: #eee; border-radius: 4px; overflow: hidden; margin: 5px 0; }
                        .completeness-fill { height: 100%; background: linear-gradient(90deg, #dc3545, #ffc107, #28a745); }
                        .search-box { width: 100%; padding: 10px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px; }
                        .no-reports { text-align: center; color: #666; font-style: italic; padding: 40px; }
                    </style>
                    <script>
                        function filterReports() {
                            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
                            const reports = document.querySelectorAll('.report-item');

                            reports.forEach(report => {
                                const text = report.textContent.toLowerCase();
                                report.style.display = text.includes(searchTerm) ? 'block' : 'none';
                            });
                        }
                    </script>
                </head>
                <body>
                    <div class="header">
                        <h1>${title}</h1>
                        <p>Comprehensive view of all manual reports with detailed information and statistics.</p>
                    </div>

                    ${stats.totalReports ? `
                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-value">${stats.totalReports}</div>
                            <div class="stat-label">Total Reports</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.avgCompleteness}%</div>
                            <div class="stat-label">Avg Completeness</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.signatureRate}%</div>
                            <div class="stat-label">Signature Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.avgProblemWords}</div>
                            <div class="stat-label">Avg Problem Words</div>
                        </div>
                    </div>
                    ` : ''}

                    <input type="text" id="searchBox" class="search-box" placeholder="Search reports..." onkeyup="filterReports()">

                    <h3>Reports</h3>
                    ${reports.length > 0 ? reports.map(report => `
                        <div class="report-item">
                            <div class="report-header">
                                <h3 class="report-title">${report.title}</h3>
                                <div class="report-meta">
                                    Created: ${new Date(report.createdAt).toLocaleDateString()} |
                                    Report Date: ${new Date(report.date).toLocaleDateString()} |
                                    Status: ${report.status} |
                                    Problems: ${report.problemCount || (report.problems ? report.problems.length : 1)}
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: between; align-items: center;">
                                    <span style="font-size: 0.9em; color: #666;">Completeness: ${report.completeness}%</span>
                                </div>
                                <div class="completeness-bar">
                                    <div class="completeness-fill" style="width: ${report.completeness}%;"></div>
                                </div>
                            </div>

                            ${report.problems && report.problems.length > 0 ?
                                report.problems.map((problem, index) => `
                                    <div class="report-section" style="border-left: 3px solid #007bff; padding-left: 15px; margin-bottom: 20px;">
                                        <div class="report-section-title" style="display: flex; align-items: center; gap: 10px;">
                                            <span style="background: #007bff; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.8em; font-weight: bold;">${index + 1}</span>
                                            Problem: ${problem.name || `Problem ${index + 1}`}
                                        </div>

                                        <div style="margin-left: 34px;">
                                            <div style="margin-bottom: 10px;">
                                                <strong>Description:</strong>
                                                <div class="report-content">${problem.description}</div>
                                            </div>

                                            <div style="margin-bottom: 10px;">
                                                <strong>Solution:</strong>
                                                <div class="report-content">${problem.solution}</div>
                                            </div>

                                            ${problem.additional ? `
                                            <div style="margin-bottom: 10px;">
                                                <strong>Additional Details:</strong>
                                                <div class="report-content">${problem.additional}</div>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                `).join('')
                                :
                                `
                                <div class="report-section">
                                    <div class="report-section-title">Problem Description</div>
                                    <div class="report-content">${report.problem || 'No problem description'}</div>
                                </div>

                                <div class="report-section">
                                    <div class="report-section-title">Solution & Fix Applied</div>
                                    <div class="report-content">${report.solution || 'No solution description'}</div>
                                </div>

                                ${report.additional ? `
                                <div class="report-section">
                                    <div class="report-section-title">Additional Details</div>
                                    <div class="report-content">${report.additional}</div>
                                </div>
                                ` : ''}
                                `
                            }

                            <div class="signatures">
                                <div class="signature">
                                    <div class="signature-label">Employee Signature</div>
                                    <div class="signature-name">${report.signatures.employee.name || '(Not signed)'}</div>
                                    <div style="font-size: 0.8em; color: #666;">
                                        ${report.signatures.employee.date ? new Date(report.signatures.employee.date).toLocaleDateString() : ''}
                                    </div>
                                </div>
                                <div class="signature">
                                    <div class="signature-label">Supervisor Signature</div>
                                    <div class="signature-name">${report.signatures.supervisor.name || '(Not signed)'}</div>
                                    <div style="font-size: 0.8em; color: #666;">
                                        ${report.signatures.supervisor.date ? new Date(report.signatures.supervisor.date).toLocaleDateString() : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('') : '<div class="no-reports">No reports found.</div>'}
                </body>
                </html>
            `;
        }

        async function updateManualReportsStatus() {
            const statusElement = document.getElementById('manualReportsStatus');
            if (!statusElement) return;

            try {
                const response = await fetch('/.netlify/functions/get-manual-reports?includeStats=true&limit=1');
                if (response.ok) {
                    const data = await response.json();
                    const stats = data.statistics || {};

                    statusElement.innerHTML = `
                        Total reports: <strong>${stats.totalReports || 0}</strong> |
                        Average completeness: <strong>${stats.avgCompleteness || 0}%</strong> |
                        Signature rate: <strong>${stats.signatureRate || 0}%</strong>
                    `;
                } else {
                    statusElement.innerHTML = 'No reports created yet';
                }
            } catch (error) {
                statusElement.innerHTML = 'Error loading reports status';
            }
        }

        // Snapshot management functions
        async function createManualSnapshot() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner"></span> Creating...';
            button.disabled = true;

            try {
                const scheduler = window.dailySchedulerInstance || DailyScheduler.getInstance();
                await scheduler.createManualSnapshot();

                // Refresh dashboard data
                if (dashboardInstance) {
                    await dashboardInstance.loadData();
                    dashboardInstance.updateMetrics();
                }

                updateSnapshotStatus();
            } catch (error) {
                console.error('Error creating manual snapshot:', error);
                if (dashboardInstance) {
                    dashboardInstance.showAlert('Error creating snapshot', 'error');
                }
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        async function viewSnapshotHistory() {
            try {
                const scheduler = window.dailySchedulerInstance || DailyScheduler.getInstance();
                const historicalData = await scheduler.getHistoricalData('30d');

                // Open history in new window
                const historyWindow = window.open('', '_blank', 'width=1000,height=700');
                historyWindow.document.write(generateHistoryHTML(historicalData));
            } catch (error) {
                console.error('Error viewing snapshot history:', error);
                if (dashboardInstance) {
                    dashboardInstance.showAlert('Error loading snapshot history', 'error');
                }
            }
        }

        async function backfillSnapshots() {
            if (!confirm('This will create historical snapshots for the past 30 days. This may take a few minutes. Continue?')) {
                return;
            }

            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner"></span> Backfilling...';
            button.disabled = true;

            try {
                const scheduler = window.dailySchedulerInstance || DailyScheduler.getInstance();
                const results = await scheduler.backfillHistoricalData(30);

                if (dashboardInstance) {
                    dashboardInstance.showAlert(`Backfill completed. Created ${results.length} snapshots.`, 'success');
                    // Refresh dashboard data
                    await dashboardInstance.loadData();
                }

                updateSnapshotStatus();
            } catch (error) {
                console.error('Error during backfill:', error);
                if (dashboardInstance) {
                    dashboardInstance.showAlert('Error during backfill process', 'error');
                }
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        function updateSnapshotStatus() {
            const statusElement = document.getElementById('snapshotStatus');
            if (!statusElement) return;

            try {
                const scheduler = window.dailySchedulerInstance || DailyScheduler.getInstance();
                const status = scheduler.getSchedulerStatus();

                const nextSnapshot = new Date(status.nextSnapshot);
                const lastSnapshot = status.lastSnapshot || 'Never';

                statusElement.innerHTML = `
                    Last snapshot: <strong>${lastSnapshot}</strong> |
                    Next snapshot: <strong>${nextSnapshot.toLocaleDateString()} at ${nextSnapshot.toLocaleTimeString()}</strong> |
                    Auto-snapshot: <strong>${status.autoSnapshot ? 'Enabled' : 'Disabled'}</strong>
                `;
            } catch (error) {
                statusElement.innerHTML = 'Error loading snapshot status';
            }
        }

        function generateHistoryHTML(historicalData) {
            const snapshots = historicalData.snapshots || [];
            const summary = historicalData.summary || {};

            return `
                <html>
                <head>
                    <title>Snapshot History</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                        .snapshot { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
                        .snapshot-header { font-weight: bold; color: #333; margin-bottom: 10px; }
                        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
                        .metric { background: white; padding: 10px; border-radius: 3px; }
                        .metric-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
                        .metric-label { font-size: 0.9em; color: #666; }
                    </style>
                </head>
                <body>
                    <h1>Daily Snapshots History</h1>
                    <div class="summary">
                        <h3>Summary (${summary.totalDays || 0} days)</h3>
                        <p><strong>Date Range:</strong> ${summary.dateRange?.from || 'N/A'} to ${summary.dateRange?.to || 'N/A'}</p>
                        <p><strong>Average Tasks/Day:</strong> ${summary.averageTasksPerDay || 0}</p>
                        <p><strong>Average Completion Rate:</strong> ${summary.averageCompletionRate || 0}%</p>
                        <p><strong>Total Tasks Completed:</strong> ${summary.totalTasksCompleted || 0}</p>
                    </div>

                    <h3>Daily Snapshots</h3>
                    ${snapshots.map(snapshot => `
                        <div class="snapshot">
                            <div class="snapshot-header">${snapshot.date}</div>
                            <div class="metrics">
                                <div class="metric">
                                    <div class="metric-value">${snapshot.tasks?.completed || 0}</div>
                                    <div class="metric-label">Tasks Completed</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">${snapshot.tasks?.total || 0}</div>
                                    <div class="metric-label">Total Tasks</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">${snapshot.equipment?.total || 0}</div>
                                    <div class="metric-label">Equipment Items</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">${Math.round(snapshot.metrics?.task?.completionRate || 0)}%</div>
                                    <div class="metric-label">Completion Rate</div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </body>
                </html>
            `;
        }

        // Initialize dashboard when page loads
        let dashboardInstance;
        document.addEventListener('DOMContentLoaded', async () => {
            dashboardInstance = new DashboardAnalytics();

            // Update status displays after dashboard loads
            setTimeout(() => {
                updateSnapshotStatus();
                updateArchivalStatusDashboard();
                updateManualReportsStatus();
            }, 1000);
        });
    </script>
</body>
</html>
